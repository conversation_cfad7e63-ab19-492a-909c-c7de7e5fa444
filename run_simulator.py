#!/usr/bin/env python3
"""
Launcher script for the ARINC Flight Simulator.

This script runs the ARINC Flight Simulator, preferring the installed package version
but falling back to the development version if needed.

For installed package only: use run_installed_simulator.py
For development version only: run from src directory
"""

import sys

def main():
    """Run the installed ARINC Flight Simulator package."""
    try:
        # Try to import the installed package (not from local src)
        import arinc_flight_simulator

        # Check if this is the installed version by checking the module path
        import os
        module_path = arinc_flight_simulator.__file__

        # Get current directory, handling cases where __file__ might not be defined
        try:
            current_dir = os.path.dirname(os.path.abspath(__file__))
        except NameError:
            # __file__ is not defined (e.g., when run as string), use current working directory
            current_dir = os.getcwd()

        # If the module is from the local src directory, it's development mode
        if current_dir in module_path:
            print("WARNING: Running in development mode (using local src directory)")
            print("   To run the installed package version:")
            print("   1. Install the package: pip install dist/arinc_flight_simulator-1.0.0-py3-none-any.whl")
            print("   2. Run: arinc-simulator")
            print()
            print("   Continuing with development version...")
        else:
            print(f"SUCCESS: Running installed ARINC Flight Simulator v{arinc_flight_simulator.__version__}")
            print(f"   Package location: {os.path.dirname(module_path)}")

        # Run the simulator
        from arinc_flight_simulator.main import main as simulator_main
        return simulator_main()

    except ImportError:
        print("ERROR: ARINC Flight Simulator package is not installed.")
        print()
        print("To install the package, run:")
        print("  pip install dist/arinc_flight_simulator-1.0.0-py3-none-any.whl")
        print()
        print("Or install from the current directory:")
        print("  pip install .")
        print()
        print("After installation, you can run the simulator with:")
        print("  arinc-simulator")
        print("  or")
        print("  python -m arinc_flight_simulator")
        return 1

if __name__ == "__main__":
    sys.exit(main())
