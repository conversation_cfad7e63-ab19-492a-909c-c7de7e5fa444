#!/usr/bin/env python3
"""
Setup script for ARINC Flight Simulator package.
This is a fallback setup.py for compatibility with older pip versions.
The main configuration is in pyproject.toml.
"""

from setuptools import setup, find_packages
import os

# Read the README file
def read_readme():
    readme_path = os.path.join(os.path.dirname(__file__), 'README.md')
    if os.path.exists(readme_path):
        with open(readme_path, 'r', encoding='utf-8') as f:
            return f.read()
    return ""

setup(
    name="arinc_flight_simulator",
    use_scm_version=False,
    version="1.0.0",
    description="A Python-based flight simulator that generates ARINC 429 data for aviation applications",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    author="ARINC Flight Simulator Team",
    python_requires=">=3.8",
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    package_data={
        "arinc_flight_simulator.config": ["*.ini"],
    },
    entry_points={
        "console_scripts": [
            "arinc-simulator=arinc_flight_simulator.main:main",
        ],
    },
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "Intended Audience :: Science/Research",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Scientific/Engineering :: Physics",
        "Topic :: Software Development :: Libraries :: Python Modules",
    ],
    keywords="aviation arinc flight-simulator arinc-429",
    install_requires=[],
    extras_require={
        "dev": [
            "pytest>=6.0",
            "pytest-cov",
            "black",
            "flake8",
            "mypy",
        ],
    },
)
